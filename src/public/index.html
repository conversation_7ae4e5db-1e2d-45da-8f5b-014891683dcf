<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Card Crawler Settings</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2 {
      color: #333;
      padding-bottom: 10px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="number"], input[type="text"], textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    .note {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
      margin-bottom: 0;
    }
    textarea {
      min-height: 80px;
      font-family: monospace;
      font-size: 14px;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
    }

    button:hover {
      background-color: #45a049;
    }

    section.header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      border-bottom: 1px solid #ddd;
    }

    .message {
      padding: 15px;
      border-radius: 4px;
      display: none;
      position: fixed;
      top: 20px;
      right: 20px;
      min-width: 250px;
      max-width: 350px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
      z-index: 100;
      animation: slide-in 0.5s ease-out;
    }
    @keyframes slide-in {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    @keyframes fade-out {
      from {
        transform: translateX(0);
        opacity: 1;
      }
      to {
        transform: translateX(100%);
        opacity: 0;
      }
    }
    .message-content {
      margin-bottom: 5px;
      font-weight: bold;
    }
    .message-close {
      position: absolute;
      top: 5px;
      right: 5px;
      background: transparent;
      border: none;
      color: inherit;
      font-size: 18px;
      cursor: pointer;
      padding: 0;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
    }
    .message-close:hover {
      background-color: rgba(0,0,0,0.1);
      border-radius: 50%;
    }
    .message.fade-out {
      animation: fade-out 0.5s ease-in forwards;
    }
    .success {
      color: #155724;
    }
    .error {
      color: #721c24;
    }
    .info {
      color: #004085;
    }
    .section {
      margin-bottom: 30px;
      padding: 20px;
      background-color: #f9f9f9;
      border-radius: 5px;
      border: 1px solid #eee;
    }
    .collapsed .section-content {
      display: none;
    }
    .section-header {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .toggle-icon:after {
      content: "▼";
      font-size: 12px;
    }
    .collapsed .toggle-icon:after {
      content: "►";
    }

    .save-button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 0;
    }

    .save-button:hover:not(:disabled) {
      background-color: #45a049;
    }

    .save-button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }

    /* Modal Styles */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
      background-color: #fefefe;
      margin: 5% auto;
      padding: 20px;
      border: 1px solid #888;
      border-radius: 5px;
      width: 80%;
      max-width: 600px;
      max-height: 80vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ddd;
    }

    .modal-header h2 {
      margin: 0;
      color: #333;
    }

    .close {
      color: #aaa;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      line-height: 1;
    }

    .close:hover,
    .close:focus {
      color: black;
    }

    .log-container {
      flex: 1;
      overflow-y: auto;
      background-color: #1e1e1e;
      color: #ffffff;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      padding: 15px;
      border-radius: 4px;
      white-space: pre-wrap;
      word-wrap: break-word;
      max-height: 400px;
    }

    .log-entry {
      margin-bottom: 5px;
      padding: 2px 0;
    }

    .modal-footer {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      font-weight: bold;
    }

    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 8px;
      animation: pulse 1.5s infinite;
    }

    .status-dot.running {
      background-color: #4caf50;
    }

    .status-dot.stopped {
      background-color: #f44336;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

  </style>
</head>
<body>
  <section class="header">
    <h1>Card Crawler Settings</h1>
    <div>
      <button type="submit" form="settingsForm" class="save-button" id="saveButton" disabled>Save Settings</button>
      <button type="button" id="startCrawlingButton" class="save-button" style="background-color: #007bff; margin-left: 10px;">Start Crawling</button>
    </div>
  </section>

  <form id="settingsForm">
    <div class="section" id="connectionSection">
      <div class="section-header" onclick="toggleSection('connectionSection')">
        <h2>Connection Settings</h2>
        <span class="toggle-icon"></span>
      </div>
      <div class="section-content">
        <div class="form-group">
          <label for="targetHost">Target Host:</label>
          <input type="text" id="targetHost" name="targetHost" placeholder="e.g., example.com" required>
        </div>

        <div class="form-group">
          <label for="userAgent">User Agent:</label>
          <input type="text" id="userAgent" name="userAgent" placeholder="e.g., Mozilla/5.0...">
        </div>

        <div class="form-group">
          <label for="cookies">Cookies:</label>
          <textarea id="cookies" rows="10" name="cookies" placeholder="Paste your session cookies here (format: name1=value1; name2=value2; ...)"></textarea>
        </div>
      </div>
    </div>

    <div class="section" id="searchSection">
      <div class="section-header" onclick="toggleSection('searchSection')">
        <h2>Search Parameters</h2>
        <span class="toggle-icon"></span>
      </div>
      <div class="section-content">
        <div class="form-group">
          <label for="price_to">Maximum Price:</label>
          <input type="number" id="price_to" name="price_to" min="0" step="0.01">
        </div>

        <div class="form-group">
          <label for="min_rating">Minimum Rating:</label>
          <input type="number" id="min_rating" name="min_rating" min="0" max="5" step="0.1">
        </div>

        <div class="form-group">
          <label for="min_reviews">Minimum Reviews:</label>
          <input type="number" id="min_reviews" name="min_reviews" min="0" step="1">
        </div>

        <div class="form-group">
          <label for="bins">BIN Numbers (comma-separated):</label>
          <input type="text" id="bins" name="bins" placeholder="e.g., 547511, 556682, 403571">
        </div>

        <div class="form-group">
          <label for="countries">Countries (comma-separated):</label>
          <input type="text" id="countries" name="countries" placeholder="e.g., US, UK, CA">
          <p class="note">Leave empty to include all countries</p>
        </div>

        <div class="form-group">
          <label for="min_valid">Minimum Valid %:</label>
          <input type="number" id="min_valid" name="min_valid" min="0" max="100" step="1" placeholder="e.g., 70">
          <p class="note">Only show cards with validity percentage >= this value</p>
        </div>

        <div class="form-group">
          <label for="max_days_old">Max Days Old:</label>
          <input type="number" id="max_days_old" name="max_days_old" min="0" step="1" placeholder="e.g., 2">
          <p class="note">Only show cards with database date within this many days from today</p>
        </div>
      </div>
    </div>

    <div class="section" id="crawlerSection">
      <div class="section-header" onclick="toggleSection('crawlerSection')">
        <h2>Crawler Settings</h2>
        <span class="toggle-icon"></span>
      </div>
      <div class="section-content">
        <div class="form-group">
          <label for="limitPerRun">Max Cards Per Run:</label>
          <input type="number" id="limitPerRun" name="limitPerRun" min="1" step="1">
          <p class="note">Maximum number of cards to buy in one run</p>
        </div>

        <div class="form-group">
          <label for="delayPerRun">Delay Per Run (seconds):</label>
          <input type="number" id="delayPerRun" name="delayPerRun" min="1" step="1">
          <p class="note">Delay between runs to prevent rate limiting</p>
        </div>

        <div class="form-group">
          <label for="isFakeCheckout">
            <input type="checkbox" id="isFakeCheckout" name="isFakeCheckout" style="width: auto; margin-right: 8px;">
            Fake Checkout Mode
          </label>
          <p class="note">Enable to test without actual payment processing</p>
        </div>
      </div>
    </div>

    <div class="section" id="telegramSection">
      <div class="section-header" onclick="toggleSection('telegramSection')">
        <h2>Telegram Notifications</h2>
        <span class="toggle-icon"></span>
      </div>
      <div class="section-content">
        <div class="form-group">
          <label for="telegramBotToken">Bot Token:</label>
          <input type="text" id="telegramBotToken" name="telegramBotToken" placeholder="e.g., 123456789:ABCdefGHIjklMNOpqrsTUVwxyz">
          <p class="note">Your Telegram bot token for sending notifications</p>
        </div>

        <div class="form-group">
          <label for="telegramChannelId">Channel ID:</label>
          <input type="text" id="telegramChannelId" name="telegramChannelId" placeholder="e.g., -1001234567890">
          <p class="note">Telegram channel ID where notifications will be sent</p>
        </div>
      </div>
    </div>
  </form>

  <div id="message" class="message">
    <button id="messageClose" class="message-close">×</button>
    <div id="messageContent" class="message-content"></div>
  </div>

  <!-- Crawling Modal -->
  <div id="crawlingModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Card Crawler - Live Status</h2>
        <span class="close" id="modalClose">&times;</span>
      </div>
      <div class="log-container" id="logContainer">
        <div class="log-entry info">Initializing crawler...</div>
      </div>
      <div class="modal-footer">
        <div class="status-indicator">
          <div class="status-dot stopped" id="statusDot"></div>
          <span id="statusText">Stopped</span>
        </div>
        <button id="stopCrawlingButton" class="save-button" style="background-color: #f44336; display: none;">Stop Crawling</button>
      </div>
    </div>
  </div>

  <script>
    // Default settings
    const defaultSettings = {
      targetHost: "stashpatrick.gl",
      userAgent: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      cookies: "",
      searchParams: {
        price_to: 25,
        min_rating: 2.8,
        min_reviews: 20,
        bins: [547511, 556682, 403571, 516566, 547679, 454925, 414846, 430999]
      },
      countries: []
    };

    // Toggle section collapse/expand
    function toggleSection(sectionId) {
      const section = document.getElementById(sectionId);
      section.classList.toggle('collapsed');
    }


    // Load current settings when page loads
    document.addEventListener('DOMContentLoaded', async () => {
      try {
        const response = await fetch('/api/settings');
        if (!response.ok) {
          throw new Error('Failed to load settings');
        }

        const data = await response.json();

        // Connection settings
        document.getElementById('targetHost').value = data.targetHost || '';
        document.getElementById('userAgent').value = data.userAgent || '';
        document.getElementById('cookies').value = data.cookies || '';

        // Search parameters
        if (data.searchParams) {
          document.getElementById('price_to').value = data.searchParams.price_to || '';
          document.getElementById('min_rating').value = data.searchParams.min_rating || '';
          document.getElementById('min_reviews').value = data.searchParams.min_reviews || '';
          document.getElementById('min_valid').value = data.searchParams.min_valid || '';
          document.getElementById('max_days_old').value = data.searchParams.max_days_old || '';

          if (Array.isArray(data.searchParams.bins)) {
            document.getElementById('bins').value = data.searchParams.bins.join(', ');
          }

          if (Array.isArray(data.searchParams.countries)) {
            document.getElementById('countries').value = data.searchParams.countries.join(', ');
          }
        }

        // Crawler settings
        document.getElementById('limitPerRun').value = data.limitPerRun || 1;
        document.getElementById('delayPerRun').value = data.delayPerRun || 5;
        document.getElementById('isFakeCheckout').checked = data.isFakeCheckout !== undefined ? data.isFakeCheckout : true;

        // Telegram settings
        document.getElementById('telegramBotToken').value = data.telegramBotToken || '';
        document.getElementById('telegramChannelId').value = data.telegramChannelId || '';
      } catch (error) {
        showMessage('Error loading settings: ' + error.message, false);
      }
    });

    // Helper function to show status messages
    function showMessage(text, isSuccess) {
      const messageElement = document.getElementById('message');
      const messageContent = document.getElementById('messageContent');

      // Clear any existing timeout
      if (window.messageTimeout) {
        clearTimeout(window.messageTimeout);
        messageElement.classList.remove('fade-out');
      }

      messageContent.textContent = text;
      messageElement.className = `message ${isSuccess ? 'success' : isSuccess === null ? 'info' : 'error'}`;

      messageElement.style.display = 'block';

      // Add event listener to close button
      document.getElementById('messageClose').addEventListener('click', function() {
        hideMessage();
      });

      // Auto-hide after 5 seconds
      window.messageTimeout = setTimeout(() => {
        hideMessage();
      }, 5000);
    }

    function hideMessage() {
      const messageElement = document.getElementById('message');
      messageElement.classList.add('fade-out');

      // Wait for animation to complete before hiding
      setTimeout(() => {
        messageElement.style.display = 'none';
        messageElement.classList.remove('fade-out');
      }, 500);
    }

    // Enable save button after page loads
    document.addEventListener('DOMContentLoaded', function() {
      // Enable the save button after settings are loaded
      setTimeout(() => {
        document.getElementById('saveButton').disabled = false;
      }, 500);
    });

    // Handle form submission
    document.getElementById('settingsForm').addEventListener('submit', async (e) => {
      e.preventDefault();

      const targetHost = document.getElementById('targetHost').value.trim();
      const userAgent = document.getElementById('userAgent').value.trim();
      const cookies = document.getElementById('cookies').value.trim();

      const price_to = parseFloat(document.getElementById('price_to').value);
      const min_rating = parseFloat(document.getElementById('min_rating').value);
      const min_reviews = parseInt(document.getElementById('min_reviews').value);
      const min_valid = parseInt(document.getElementById('min_valid').value);
      const max_days_old = parseInt(document.getElementById('max_days_old').value);
      const binsString = document.getElementById('bins').value;

      // Parse bins from comma-separated string to array of integers
      const bins = binsString.split(',')
        .map(bin => bin.trim())
        .filter(bin => bin.length > 0)
        .map(bin => parseInt(bin, 10));

      // Parse countries from comma-separated string to array of strings
      const countriesString = document.getElementById('countries').value;
      const countries = countriesString.split(',')
        .map(country => country.trim())
        .filter(country => country.length > 0);

      // Get crawler settings
      const limitPerRun = parseInt(document.getElementById('limitPerRun').value) || 1;
      const delayPerRun = parseInt(document.getElementById('delayPerRun').value) || 5;
      const isFakeCheckout = document.getElementById('isFakeCheckout').checked;

      // Get Telegram settings
      const telegramBotToken = document.getElementById('telegramBotToken').value.trim();
      const telegramChannelId = document.getElementById('telegramChannelId').value.trim();

      const settings = {
        targetHost,
        userAgent,
        cookies,
        sampleProxies: [],
        telegramBotToken,
        telegramChannelId,
        isDebug: false,
        isFakeCheckout,
        limitPerRun,
        delayPerRun,
        searchParams: {
          price_to,
          min_rating,
          min_reviews,
          min_valid,
          max_days_old,
          bins,
          countries
        }
      };

      try {
        // Show saving indicator
        showMessage('Saving settings...', null);

        const response = await fetch('/api/settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(settings),
        });

        if (!response.ok) {
          throw new Error('Failed to save settings');
        }

        showMessage('Settings saved successfully!', true);
      } catch (error) {
        showMessage('Error saving settings: ' + error.message, false);
      }
    });

    // Crawling Modal Logic
    let ws = null;
    let isCrawling = false;

    const crawlingModal = document.getElementById('crawlingModal');
    const startCrawlingButton = document.getElementById('startCrawlingButton');
    const stopCrawlingButton = document.getElementById('stopCrawlingButton');
    const modalClose = document.getElementById('modalClose');
    const logContainer = document.getElementById('logContainer');
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');

    // Modal controls
    function openModal() {
      crawlingModal.style.display = 'block';
      clearLog();
      addLogEntry('info', 'Modal opened. Ready to start crawling...');
    }

    function closeModal() {
      crawlingModal.style.display = 'none';
      if (ws) {
        ws.close();
        ws = null;
      }
    }

    function clearLog() {
      logContainer.innerHTML = '';
    }

    function addLogEntry(type, message) {
      const timestamp = new Date().toLocaleTimeString();
      const logEntry = document.createElement('div');
      logEntry.className = `log-entry ${type}`;
      logEntry.textContent = `[${timestamp}] ${message}`;
      logContainer.appendChild(logEntry);

      // Auto-scroll to bottom
      logContainer.scrollTop = logContainer.scrollHeight;
    }

    function updateStatus(running) {
      isCrawling = running;
      statusDot.className = `status-dot ${running ? 'running' : 'stopped'}`;
      statusText.textContent = running ? 'Running' : 'Stopped';
      startCrawlingButton.disabled = running;
      stopCrawlingButton.style.display = running ? 'inline-block' : 'none';
    }

    function initWebSocket() {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws`;

      ws = new WebSocket(wsUrl);

      ws.onopen = function() {
        addLogEntry('success', 'Connected to crawler service');
        updateStatus(true);
      };

                      ws.onmessage = function(event) {
          const data = JSON.parse(event.data);
          addLogEntry(data.type || 'info', data.message);

          // Keep status running on heartbeat messages
          if (data.message.includes('💓 Crawler heartbeat') ||
              data.message.includes('🔄 Starting new crawling iteration')) {
            updateStatus(true);
          }
          // Only stop on critical errors or explicit stop messages
          else if (data.message.includes('process exited') ||
                   data.message.includes('Crawling process completed successfully') ||
                   data.message.includes('forcefully terminated') ||
                   data.message.includes('Failed to start crawling process')) {
            updateStatus(false);
          }
        };

      ws.onclose = function() {
        addLogEntry('warn', 'Connection closed');
        updateStatus(false);
        ws = null;
      };

      ws.onerror = function(error) {
        addLogEntry('error', 'WebSocket error: ' + error.message);
        updateStatus(false);
      };
    }

    // Event listeners
    startCrawlingButton.addEventListener('click', async function() {
      try {
        // First check if settings are valid
        const response = await fetch('/api/settings');
        if (!response.ok) {
          showMessage('Please configure settings first', false);
          return;
        }

        openModal();
        initWebSocket();

        // Start crawling
        const startResponse = await fetch('/api/start-crawling', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (!startResponse.ok) {
          const error = await startResponse.text();
          addLogEntry('error', `Failed to start crawling: ${error}`);
          updateStatus(false);
        }
      } catch (error) {
        addLogEntry('error', `Error starting crawler: ${error.message}`);
        updateStatus(false);
      }
    });

    stopCrawlingButton.addEventListener('click', async function() {
      try {
        const response = await fetch('/api/stop-crawling', {
          method: 'POST'
        });

        if (response.ok) {
          addLogEntry('warn', 'Stop signal sent to crawler');
        } else {
          addLogEntry('error', 'Failed to send stop signal');
        }
      } catch (error) {
        addLogEntry('error', `Error stopping crawler: ${error.message}`);
      }
    });

    modalClose.addEventListener('click', closeModal);

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === crawlingModal) {
        closeModal();
      }
    });


  </script>
</body>
</html>
